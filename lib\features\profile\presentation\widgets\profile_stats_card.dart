import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../../core/providers/profile_provider.dart';
import '../../../../core/providers/database_auth_provider.dart';

class ProfileStatsCard extends StatelessWidget {
  const ProfileStatsCard({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer2<ProfileProvider, DatabaseAuthProvider>(
      builder: (context, profileProvider, authProvider, child) {
        return Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: const Color(0xFF2A2A2A),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildStatItem(
                icon: Icons.favorite,
                label: 'Matches',
                value: profileProvider.matches.toString(),
                color: const Color(0xFFFF6B9D),
              ),
              _buildDivider(),
              _buildStatItem(
                icon: Icons.thumb_up,
                label: 'Likes',
                value: profileProvider.likes.toString(),
                color: const Color(0xFF4CAF50),
              ),
              _buildDivider(),
              _buildStatItem(
                icon: Icons.visibility,
                label: 'Views',
                value: profileProvider.views.toString(),
                color: const Color(0xFF2196F3),
              ),
              _buildDivider(),
              _buildStatItem(
                icon: Icons.monetization_on,
                label: 'Coins',
                value: authProvider.coinBalance.toString(),
                color: const Color(0xFFFFD700),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: color,
            size: 20,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(
            color: Colors.grey,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildDivider() {
    return Container(
      width: 1,
      height: 40,
      color: Colors.grey.withValues(alpha: 0.3),
    );
  }
}
