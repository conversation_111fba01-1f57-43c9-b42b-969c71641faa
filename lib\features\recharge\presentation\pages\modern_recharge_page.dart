import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../../core/providers/database_auth_provider.dart';
import '../../../../core/services/coin_service.dart';
import '../widgets/coin_balance_card.dart';
import '../widgets/coin_package_card.dart';
import '../widgets/coin_features_card.dart';
import '../widgets/payment_method_selector.dart';

class ModernRechargePage extends StatefulWidget {
  const ModernRechargePage({super.key});

  @override
  State<ModernRechargePage> createState() => _ModernRechargePageState();
}

class _ModernRechargePageState extends State<ModernRechargePage> {
  final CoinService _coinService = CoinService();
  String? _selectedPackageId;
  String _selectedPaymentMethod = 'card';
  bool _isLoading = false;

  List<Map<String, dynamic>> _coinPackages = [];

  @override
  void initState() {
    super.initState();
    _loadCoinPackages();
  }

  Future<void> _loadCoinPackages() async {
    try {
      final packages = await _coinService.getCoinPackages();
      if (mounted) {
        setState(() {
          _coinPackages = packages;
        });
      }
    } catch (e) {
      // Handle error
      print('Error loading coin packages: $e');
    }
  }

  String _formatPrice(dynamic price) {
    if (price is String) {
      return double.parse(price).toStringAsFixed(0);
    } else if (price is double) {
      return price.toStringAsFixed(0);
    } else if (price is int) {
      return price.toString();
    }
    return price.toString();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A1A),
      body: Consumer<DatabaseAuthProvider>(
        builder: (context, authProvider, child) {
          return CustomScrollView(
            slivers: [
              // App Bar
              SliverAppBar(
                expandedHeight: 120,
                floating: false,
                pinned: true,
                backgroundColor: const Color(0xFF2A2A2A),
                flexibleSpace: FlexibleSpaceBar(
                  title: const Text(
                    'Buy Coins',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  background: Container(
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Color(0xFFFFD700),
                          Color(0xFFFF6B9D),
                        ],
                      ),
                    ),
                  ),
                ),
              ),

              // Content
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      // Current Balance
                      CoinBalanceCard(
                        balance: authProvider.coinBalance,
                        onHistoryTap: _showTransactionHistory,
                      ),

                      const SizedBox(height: 24),

                      // Package Selection
                      _buildSectionHeader('Choose Your Package'),
                      const SizedBox(height: 16),

                      // Coin Packages
                      ..._coinPackages.map((package) => Padding(
                            padding: const EdgeInsets.only(bottom: 12),
                            child: CoinPackageCard(
                              package: package,
                              isSelected: _selectedPackageId == package['id'],
                              onTap: () {
                                print('Package selected: ${package['id']}');
                                if (mounted) {
                                  setState(() {
                                    _selectedPackageId = package['id'];
                                  });
                                }
                              },
                            ),
                          )),

                      const SizedBox(height: 24),

                      // Payment Method
                      if (_selectedPackageId != null) ...[
                        _buildSectionHeader('Payment Method'),
                        const SizedBox(height: 16),
                        PaymentMethodSelector(
                          selectedMethod: _selectedPaymentMethod,
                          onMethodChanged: (method) {
                            setState(() {
                              _selectedPaymentMethod = method;
                            });
                          },
                        ),
                        const SizedBox(height: 24),
                      ],

                      // Purchase Button
                      if (_selectedPackageId != null) _buildPurchaseButton(),

                      const SizedBox(height: 24),

                      // Features Info
                      const CoinFeaturesCard(),

                      const SizedBox(height: 24),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Align(
      alignment: Alignment.centerLeft,
      child: Text(
        title,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildPurchaseButton() {
    try {
      final selectedPackage = _coinPackages.firstWhere(
        (package) => package['id'] == _selectedPackageId,
        orElse: () => {},
      );

      if (selectedPackage.isEmpty) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: const Text(
            'Package not found',
            style: TextStyle(color: Colors.red),
          ),
        );
      }

      return Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: const Color(0xFF2A2A2A),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          children: [
            // Purchase Summary
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Total:',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '₹${_formatPrice(selectedPackage['price_inr'])}',
                  style: const TextStyle(
                    color: Color(0xFFFFD700),
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'You get:',
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 14,
                  ),
                ),
                Text(
                  '${selectedPackage['coins'] + (selectedPackage['bonus_coins'] ?? 0)} coins',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // Purchase Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _handlePurchase,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFFFD700),
                  foregroundColor: Colors.black,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                ),
                child: _isLoading
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Colors.black),
                        ),
                      )
                    : const Text(
                        'Purchase Now',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
            ),

            const SizedBox(height: 12),

            // Security Note
            const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.security,
                  color: Colors.green,
                  size: 16,
                ),
                SizedBox(width: 8),
                Text(
                  'Secure payment powered by Stripe',
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    } catch (e) {
      return Container(
        padding: const EdgeInsets.all(20),
        child: Text(
          'Error loading package: $e',
          style: const TextStyle(color: Colors.red),
        ),
      );
    }
  }

  Future<void> _handlePurchase() async {
    if (_selectedPackageId == null) return;

    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    }

    try {
      final selectedPackage = _coinPackages.firstWhere(
        (package) => package['id'] == _selectedPackageId,
      );

      // Get current user ID
      final authProvider = context.read<DatabaseAuthProvider>();
      final userId = authProvider.user?.id;

      if (userId == null) {
        _showErrorDialog('User not authenticated');
        return;
      }

      // Show payment dialog first
      final paymentConfirmed = await _showPaymentDialog(selectedPackage);
      if (!paymentConfirmed) return;

      // Process purchase with backend integration
      final result = await _coinService.purchaseCoins(
        userId: userId,
        packageId: _selectedPackageId!,
        paymentMethod: _selectedPaymentMethod,
        paymentId: 'demo_${DateTime.now().millisecondsSinceEpoch}',
      );

      if (mounted && result != null) {
        // Update local state
        authProvider.updateCoins(result['new_balance'] as int);

        // Show success dialog
        _showPurchaseSuccessDialog(selectedPackage, result);

        // Reset selection
        if (mounted) {
          setState(() {
            _selectedPackageId = null;
          });
        }
      } else {
        if (mounted) {
          _showErrorDialog('Purchase failed. Please try again.');
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorDialog('An error occurred during purchase.');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<bool> _showPaymentDialog(Map<String, dynamic> package) async {
    return await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (context) => AlertDialog(
            backgroundColor: const Color(0xFF2A2A2A),
            title: const Text(
              'Confirm Payment',
              style: TextStyle(color: Colors.white),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Package: ${package['description']}',
                  style: const TextStyle(color: Colors.white),
                ),
                const SizedBox(height: 8),
                Text(
                  'Coins: ${package['coins']} + ${package['bonus_coins']} bonus',
                  style: const TextStyle(color: Colors.white),
                ),
                const SizedBox(height: 8),
                Text(
                  'Amount: ₹${_formatPrice(package['price_inr'])}',
                  style: const TextStyle(
                    color: Color(0xFFFF6B9D),
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                const Text(
                  'Demo Payment Details:',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: const Color(0xFF3A3A3A),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Card Number: 4111 1111 1111 1111',
                        style: TextStyle(color: Colors.grey, fontSize: 12),
                      ),
                      Text(
                        'Expiry: 12/25',
                        style: TextStyle(color: Colors.grey, fontSize: 12),
                      ),
                      Text(
                        'CVV: 123',
                        style: TextStyle(color: Colors.grey, fontSize: 12),
                      ),
                      Text(
                        'Name: Demo User',
                        style: TextStyle(color: Colors.grey, fontSize: 12),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'This is a demo payment. No real money will be charged.',
                  style: TextStyle(
                    color: Colors.orange,
                    fontSize: 12,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text(
                  'Cancel',
                  style: TextStyle(color: Colors.grey),
                ),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFFF6B9D),
                ),
                child: const Text(
                  'Pay Now',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
        ) ??
        false;
  }

  void _showPurchaseSuccessDialog(
      Map<String, dynamic> package, Map<String, dynamic> result) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF2A2A2A),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 64,
            ),
            const SizedBox(height: 16),
            const Text(
              'Purchase Successful!',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'You received ${result['coins_purchased']} coins',
              style: const TextStyle(color: Colors.grey),
            ),
            if (result['bonus_coins'] > 0)
              Text(
                '+ ${result['bonus_coins']} bonus coins',
                style: const TextStyle(color: Color(0xFFFF6B9D)),
              ),
            const SizedBox(height: 8),
            Text(
              'Amount Paid: ₹${_formatPrice(result['amount_paid'])}',
              style: const TextStyle(
                color: Color(0xFFFFD700),
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              'New Balance: ${result['new_balance']} coins',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => Navigator.pop(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFFF6B9D),
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text('Continue'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF2A2A2A),
        title: const Text(
          'Error',
          style: TextStyle(color: Colors.white),
        ),
        content: Text(
          message,
          style: const TextStyle(color: Colors.grey),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'OK',
              style: TextStyle(color: Color(0xFFFF6B9D)),
            ),
          ),
        ],
      ),
    );
  }

  void _showTransactionHistory() {
    // TODO: Implement transaction history
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Transaction history coming soon!'),
        backgroundColor: Color(0xFFFF6B9D),
      ),
    );
  }
}
