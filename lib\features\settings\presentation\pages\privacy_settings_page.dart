import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../../core/providers/profile_provider.dart';
import '../../../../core/constants/colors.dart';
import 'block_list_page.dart';

class PrivacySettingsPage extends StatefulWidget {
  const PrivacySettingsPage({super.key});

  @override
  State<PrivacySettingsPage> createState() => _PrivacySettingsPageState();
}

class _PrivacySettingsPageState extends State<PrivacySettingsPage> {
  bool _showOnlineStatus = true;
  bool _showDistance = true;
  bool _showAge = true;
  bool _showLastSeen = true;
  final bool _allowProfileViews = true;
  bool _showMeOnFriendy = true;
  bool _incognitoMode = false;
  bool _readReceipts = true;
  bool _typingIndicators = true;
  String _profileVisibility = 'everyone'; // everyone, matches, nobody
  String _messagePrivacy = 'matches'; // everyone, matches, premium

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  void _loadSettings() {
    final profileProvider =
        Provider.of<ProfileProvider>(context, listen: false);
    setState(() {
      _showOnlineStatus = profileProvider.showOnlineStatus;
      _showDistance = profileProvider.showDistance;
      _showAge = profileProvider.showAge;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundDark,
      appBar: AppBar(
        title: const Text(
          'Privacy Settings',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.backgroundDark,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Profile Visibility
            _buildSection(
              title: 'Profile Visibility',
              children: [
                _buildSwitchTile(
                  title: 'Show Online Status',
                  subtitle: 'Let others see when you\'re online',
                  value: _showOnlineStatus,
                  onChanged: (value) {
                    setState(() {
                      _showOnlineStatus = value;
                    });
                    _updatePrivacySettings();
                  },
                ),
                _buildSwitchTile(
                  title: 'Show Distance',
                  subtitle: 'Display your distance to other users',
                  value: _showDistance,
                  onChanged: (value) {
                    setState(() {
                      _showDistance = value;
                    });
                    _updatePrivacySettings();
                  },
                ),
                _buildSwitchTile(
                  title: 'Show Age',
                  subtitle: 'Display your age on your profile',
                  value: _showAge,
                  onChanged: (value) {
                    setState(() {
                      _showAge = value;
                    });
                    _updatePrivacySettings();
                  },
                ),
                _buildSwitchTile(
                  title: 'Show Last Seen',
                  subtitle: 'Let others see when you were last active',
                  value: _showLastSeen,
                  onChanged: (value) {
                    setState(() {
                      _showLastSeen = value;
                    });
                  },
                ),
                _buildDropdownTile(
                  title: 'Profile Visibility',
                  subtitle: 'Who can see your profile',
                  value: _profileVisibility,
                  options: const {
                    'everyone': 'Everyone',
                    'matches': 'Matches Only',
                    'nobody': 'Nobody',
                  },
                  onChanged: (value) {
                    setState(() {
                      _profileVisibility = value!;
                    });
                  },
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Discovery Settings
            _buildSection(
              title: 'Discovery',
              children: [
                _buildSwitchTile(
                  title: 'Show Me on Friendy',
                  subtitle: 'Allow others to discover your profile',
                  value: _showMeOnFriendy,
                  onChanged: (value) {
                    setState(() {
                      _showMeOnFriendy = value;
                    });
                  },
                ),
                _buildSwitchTile(
                  title: 'Incognito Mode',
                  subtitle: 'Browse profiles without being seen',
                  value: _incognitoMode,
                  onChanged: (value) {
                    setState(() {
                      _incognitoMode = value;
                    });
                    if (value) {
                      _showIncognitoDialog();
                    }
                  },
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Messaging Privacy
            _buildSection(
              title: 'Messaging',
              children: [
                _buildSwitchTile(
                  title: 'Read Receipts',
                  subtitle: 'Let others know when you\'ve read their messages',
                  value: _readReceipts,
                  onChanged: (value) {
                    setState(() {
                      _readReceipts = value;
                    });
                  },
                ),
                _buildSwitchTile(
                  title: 'Typing Indicators',
                  subtitle: 'Show when you\'re typing a message',
                  value: _typingIndicators,
                  onChanged: (value) {
                    setState(() {
                      _typingIndicators = value;
                    });
                  },
                ),
                _buildDropdownTile(
                  title: 'Who Can Message Me',
                  subtitle: 'Control who can send you messages',
                  value: _messagePrivacy,
                  options: const {
                    'everyone': 'Everyone',
                    'matches': 'Matches Only',
                    'premium': 'Premium Users Only',
                  },
                  onChanged: (value) {
                    setState(() {
                      _messagePrivacy = value!;
                    });
                  },
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Data & Privacy
            _buildSection(
              title: 'Data & Privacy',
              children: [
                _buildActionTile(
                  title: 'Download My Data',
                  subtitle: 'Get a copy of your data',
                  icon: Icons.download,
                  onTap: _downloadData,
                ),
                _buildActionTile(
                  title: 'Privacy Policy',
                  subtitle: 'Read our privacy policy',
                  icon: Icons.policy,
                  onTap: _openPrivacyPolicy,
                ),
                _buildActionTile(
                  title: 'Terms of Service',
                  subtitle: 'Read our terms of service',
                  icon: Icons.description,
                  onTap: _openTermsOfService,
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Danger Zone
            _buildSection(
              title: 'Account Management',
              children: [
                _buildActionTile(
                  title: 'Block List',
                  subtitle: 'Manage blocked users',
                  icon: Icons.block,
                  onTap: _openBlockList,
                ),
                _buildActionTile(
                  title: 'Delete Account',
                  subtitle: 'Permanently delete your account',
                  icon: Icons.delete_forever,
                  onTap: _showDeleteAccountDialog,
                  isDestructive: true,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required List<Widget> children,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.cardDark,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return ListTile(
      title: Text(
        title,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: const TextStyle(
          color: Colors.white70,
          fontSize: 14,
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: AppColors.primaryPink,
      ),
    );
  }

  Widget _buildDropdownTile({
    required String title,
    required String subtitle,
    required String value,
    required Map<String, String> options,
    required ValueChanged<String?> onChanged,
  }) {
    return ListTile(
      title: Text(
        title,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: const TextStyle(
          color: Colors.white70,
          fontSize: 14,
        ),
      ),
      trailing: DropdownButton<String>(
        value: value,
        onChanged: onChanged,
        dropdownColor: AppColors.cardDark,
        style: const TextStyle(color: Colors.white),
        underline: Container(),
        items: options.entries.map((entry) {
          return DropdownMenuItem<String>(
            value: entry.key,
            child: Text(entry.value),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildActionTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: isDestructive ? Colors.red : AppColors.primaryPink,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: isDestructive ? Colors.red : Colors.white,
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          color: isDestructive ? Colors.red.withOpacity(0.7) : Colors.white70,
          fontSize: 14,
        ),
      ),
      trailing: const Icon(
        Icons.chevron_right,
        color: Colors.white70,
      ),
      onTap: onTap,
    );
  }

  void _updatePrivacySettings() {
    final profileProvider =
        Provider.of<ProfileProvider>(context, listen: false);
    profileProvider.updateSettings({
      'show_online_status': _showOnlineStatus,
      'show_distance': _showDistance,
      'show_age': _showAge,
    });
  }

  void _showIncognitoDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.cardDark,
        title: const Text(
          'Incognito Mode',
          style: TextStyle(color: Colors.white),
        ),
        content: const Text(
          'In incognito mode, you can browse profiles without being seen. This is a premium feature.',
          style: TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _incognitoMode = false;
              });
              Navigator.pop(context);
            },
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Navigate to premium upgrade
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryPink,
            ),
            child: const Text('Upgrade to Premium'),
          ),
        ],
      ),
    );
  }

  void _downloadData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text(
            'Data download request submitted. You will receive an email within 24 hours.'),
        backgroundColor: AppColors.primaryPink,
      ),
    );
  }

  void _openPrivacyPolicy() async {
    final uri = Uri.parse('https://friendy.app/privacy');
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    }
  }

  void _openTermsOfService() async {
    final uri = Uri.parse('https://friendy.app/terms');
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    }
  }

  void _openBlockList() {
    // Navigate to block list page
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const BlockListPage(),
      ),
    );
  }

  void _showDeleteAccountDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.cardDark,
        title: const Text(
          'Delete Account',
          style: TextStyle(color: Colors.red),
        ),
        content: const Text(
          'Are you sure you want to delete your account? This action cannot be undone and all your data will be permanently removed.',
          style: TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteAccount();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: const Text('Delete Account'),
          ),
        ],
      ),
    );
  }

  void _deleteAccount() {
    // Implement account deletion
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text(
            'Account deletion initiated. You will receive a confirmation email.'),
        backgroundColor: Colors.red,
      ),
    );
  }
}
