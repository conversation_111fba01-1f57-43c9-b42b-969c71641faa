import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'core/providers/database_auth_provider.dart';
import 'features/auth/presentation/pages/modern_auth_page.dart';

void main() {
  runApp(const TestModernAuthApp());
}

class TestModernAuthApp extends StatelessWidget {
  const TestModernAuthApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => DatabaseAuthProvider()),
      ],
      child: MaterialApp(
        title: 'Modern Auth Test',
        theme: ThemeData(
          useMaterial3: true,
          colorScheme: ColorScheme.fromSeed(
            seedColor: const Color(0xFFFF6B9D),
            brightness: Brightness.dark,
          ),
          textTheme: GoogleFonts.poppinsTextTheme().apply(
            bodyColor: Colors.white,
            displayColor: Colors.white,
          ),
        ),
        home: const ModernAuthPage(),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}
