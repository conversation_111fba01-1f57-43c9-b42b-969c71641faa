import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';

class MessagingService {
  static final MessagingService _instance = MessagingService._internal();
  factory MessagingService() => _instance;

  // Mock data for mobile
  final List<Map<String, dynamic>> _mockConversations = [];
  final Map<String, List<Map<String, dynamic>>> _mockMessages = {};
  final StreamController<List<Map<String, dynamic>>> _conversationsController =
      StreamController<List<Map<String, dynamic>>>.broadcast();
  final Map<String, StreamController<List<Map<String, dynamic>>>>
      _messageControllers = {};

  MessagingService._internal() {
    _initializeMockData();
  }

  void _initializeMockData() {
    // Initialize with some demo conversations
    _mockConversations.addAll([
      {
        'id': 'conv_1',
        'otherUserId': 'user_1',
        'otherUserName': '<PERSON>',
        'otherUserAvatar':
            'https://via.placeholder.com/150/FF6B9D/FFFFFF?text=E',
        'lastMessage': 'Hey! How are you doing?',
        'lastMessageAt': DateTime.now().subtract(const Duration(minutes: 30)),
        'lastMessageSenderId': 'user_1',
        'unreadCount': 2,
      },
      {
        'id': 'conv_2',
        'otherUserId': 'user_2',
        'otherUserName': 'Sarah Johnson',
        'otherUserAvatar':
            'https://via.placeholder.com/150/FF8C42/FFFFFF?text=S',
        'lastMessage': 'That sounds great!',
        'lastMessageAt': DateTime.now().subtract(const Duration(hours: 2)),
        'lastMessageSenderId': 'current_user',
        'unreadCount': 0,
      },
      {
        'id': 'conv_3',
        'otherUserId': 'user_3',
        'otherUserName': 'Jessica Brown',
        'otherUserAvatar':
            'https://via.placeholder.com/150/9B59B6/FFFFFF?text=J',
        'lastMessage': 'See you tomorrow!',
        'lastMessageAt': DateTime.now().subtract(const Duration(days: 1)),
        'lastMessageSenderId': 'user_3',
        'unreadCount': 1,
      },
    ]);

    // Initialize mock messages
    _mockMessages['conv_1'] = [
      {
        'id': 'msg_1',
        'senderId': 'user_1',
        'content': 'Hi there! 👋',
        'type': 'text',
        'sentAt': DateTime.now().subtract(const Duration(hours: 1)),
        'readBy': ['user_1'],
      },
      {
        'id': 'msg_2',
        'senderId': 'current_user',
        'content': 'Hello! Nice to meet you!',
        'type': 'text',
        'sentAt': DateTime.now().subtract(const Duration(minutes: 45)),
        'readBy': ['current_user', 'user_1'],
      },
      {
        'id': 'msg_3',
        'senderId': 'user_1',
        'content': 'Hey! How are you doing?',
        'type': 'text',
        'sentAt': DateTime.now().subtract(const Duration(minutes: 30)),
        'readBy': ['user_1'],
      },
    ];

    _mockMessages['conv_2'] = [
      {
        'id': 'msg_4',
        'senderId': 'user_2',
        'content': 'Would you like to grab coffee sometime?',
        'type': 'text',
        'sentAt': DateTime.now().subtract(const Duration(hours: 3)),
        'readBy': ['user_2', 'current_user'],
      },
      {
        'id': 'msg_5',
        'senderId': 'current_user',
        'content': 'That sounds great!',
        'type': 'text',
        'sentAt': DateTime.now().subtract(const Duration(hours: 2)),
        'readBy': ['current_user', 'user_2'],
      },
    ];

    _mockMessages['conv_3'] = [
      {
        'id': 'msg_6',
        'senderId': 'user_3',
        'content': 'See you tomorrow!',
        'type': 'text',
        'sentAt': DateTime.now().subtract(const Duration(days: 1)),
        'readBy': ['user_3'],
      },
    ];
  }

  // Get current user ID
  String? get currentUserId => 'current_user'; // Mock user ID for mobile

  // Create or get conversation between two users
  Future<String> createOrGetConversation(String otherUserId) async {
    if (currentUserId == null) throw Exception('User not authenticated');

    final participants = [currentUserId!, otherUserId]..sort();
    final conversationId = participants.join('_');

    // Check if conversation exists in mock data
    final existingConv = _mockConversations.firstWhere(
      (conv) => conv['id'] == conversationId,
      orElse: () => {},
    );

    if (existingConv.isEmpty) {
      // Create new mock conversation
      _mockConversations.add({
        'id': conversationId,
        'otherUserId': otherUserId,
        'otherUserName': 'User $otherUserId',
        'otherUserAvatar':
            'https://via.placeholder.com/150/FF6B9D/FFFFFF?text=U',
        'lastMessage': null,
        'lastMessageAt': null,
        'lastMessageSenderId': null,
        'unreadCount': 0,
      });
      _mockMessages[conversationId] = [];
    }

    return conversationId;
  }

  // Send a text message
  Future<void> sendMessage({
    required String conversationId,
    required String content,
    String type = 'text',
  }) async {
    if (currentUserId == null) throw Exception('User not authenticated');

    // Add to mock messages
    final messageId = 'msg_${DateTime.now().millisecondsSinceEpoch}';
    final messageData = {
      'id': messageId,
      'senderId': currentUserId!,
      'content': content,
      'type': type,
      'sentAt': DateTime.now(),
      'readBy': [currentUserId!],
    };

    // Add message to conversation
    if (_mockMessages[conversationId] == null) {
      _mockMessages[conversationId] = [];
    }
    _mockMessages[conversationId]!.add(messageData);

    // Update conversation last message
    final convIndex = _mockConversations.indexWhere((conv) => conv['id'] == conversationId);
    if (convIndex != -1) {
      _mockConversations[convIndex]['lastMessage'] = content;
      _mockConversations[convIndex]['lastMessageAt'] = DateTime.now();
      _mockConversations[convIndex]['lastMessageSenderId'] = currentUserId!;
    }

    // Notify listeners
    if (_messageControllers[conversationId] != null) {
      _messageControllers[conversationId]!.add(_mockMessages[conversationId]!);
    }
    _conversationsController.add(List.from(_mockConversations));
  }

  // Send image message (mock implementation)
  Future<void> sendImageMessage({
    required String conversationId,
    required XFile imageFile,
  }) async {
    // For demo purposes, just send a text message indicating an image
    await sendMessage(
      conversationId: conversationId,
      content: '📷 Image sent',
      type: 'image',
    );
  }

  // Get messages stream for a conversation
  Stream<List<Map<String, dynamic>>> getMessagesStream(String conversationId) {
    if (_messageControllers[conversationId] == null) {
      _messageControllers[conversationId] = 
          StreamController<List<Map<String, dynamic>>>.broadcast();
    }
    
    // Emit initial data
    final messages = _mockMessages[conversationId] ?? [];
    Future.microtask(() {
      _messageControllers[conversationId]!.add(messages);
    });
    
    return _messageControllers[conversationId]!.stream;
  }

  // Get conversations stream for current user
  Stream<List<Map<String, dynamic>>> getConversationsStream() {
    if (currentUserId == null) return Stream.value([]);

    // Emit initial data
    Future.microtask(() {
      _conversationsController.add(List.from(_mockConversations));
    });
    
    return _conversationsController.stream;
  }

  // Mark messages as read (mock implementation)
  Future<void> markMessagesAsRead(String conversationId) async {
    // Update unread count
    final convIndex = _mockConversations.indexWhere((conv) => conv['id'] == conversationId);
    if (convIndex != -1) {
      _mockConversations[convIndex]['unreadCount'] = 0;
      _conversationsController.add(List.from(_mockConversations));
    }
  }

  // Delete a message (mock implementation)
  Future<void> deleteMessage(String conversationId, String messageId) async {
    final messages = _mockMessages[conversationId];
    if (messages != null) {
      messages.removeWhere((msg) => msg['id'] == messageId);
      if (_messageControllers[conversationId] != null) {
        _messageControllers[conversationId]!.add(messages);
      }
    }
  }

  // Block/Unblock user (mock implementation)
  Future<void> blockUser(String userId) async {
    debugPrint('User $userId blocked (demo)');
  }

  Future<void> unblockUser(String userId) async {
    debugPrint('User $userId unblocked (demo)');
  }

  // Check if user is blocked (mock implementation)
  Future<bool> isUserBlocked(String userId) async {
    return false; // No users blocked in demo
  }

  // Report user (mock implementation)
  Future<void> reportUser({
    required String userId,
    required String reason,
    String? description,
  }) async {
    debugPrint('User $userId reported for: $reason (demo)');
  }

  // Dispose resources
  void dispose() {
    _conversationsController.close();
    for (final controller in _messageControllers.values) {
      controller.close();
    }
  }
}
