import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../core/providers/database_auth_provider.dart';
import '../../../matching/presentation/pages/circular_match_page.dart';
import '../../../home/<USER>/pages/home_page.dart';
import '../../../streaming/presentation/pages/streaming_page.dart';

class MainApp extends StatefulWidget {
  const MainApp({super.key});

  @override
  State<MainApp> createState() => _MainAppState();
}

class _MainAppState extends State<MainApp> with TickerProviderStateMixin {
  int _currentIndex = 0;
  late AnimationController _animationController;
  late Animation<double> _animation;

  final List<MainAppTab> _tabs = [
    const MainAppTab(
      icon: Icons.explore,
      activeIcon: Icons.explore,
      label: 'Discover',
      color: Color(0xFFFF6B9D),
    ),
    const MainAppTab(
      icon: Icons.live_tv,
      activeIcon: Icons.live_tv,
      label: 'Streaming',
      color: Color(0xFFFF0000),
    ),
    const MainAppTab(
      icon: Icons.chat_bubble_outline,
      activeIcon: Icons.chat_bubble,
      label: 'Messages',
      color: Color(0xFF2196F3),
    ),
    const MainAppTab(
      icon: Icons.favorite_border,
      activeIcon: Icons.favorite,
      label: 'Match',
      color: Color(0xFFE91E63),
    ),
    const MainAppTab(
      icon: Icons.person_outline,
      activeIcon: Icons.person,
      label: 'Profile',
      color: Color(0xFF9C27B0),
    ),
  ];

  late final List<Widget> _pages;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    _pages = [
      const HomePage(), // Discover Page
      const StreamingPage(), // Streaming Page
      const Center(child: Text('Messages Page')), // Messages Page
      const CircularMatchPage(), // Match Page
      const Center(child: Text('Profile Page')), // Profile Page
    ];

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTabTapped(int index) {
    if (index != _currentIndex) {
      setState(() {
        _currentIndex = index;
      });

      // Add haptic feedback
      // HapticFeedback.lightImpact();

      // Animate tab change
      _animationController.reset();
      _animationController.forward();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<DatabaseAuthProvider>(
      builder: (context, authProvider, child) {
        // Show loading if auth is in progress
        if (authProvider.status == AuthStatus.loading) {
          return _buildLoadingScreen();
        }

        // Redirect to auth if not authenticated
        if (authProvider.status != AuthStatus.authenticated ||
            authProvider.user == null) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            Navigator.of(context).pushReplacementNamed('/auth');
          });
          return _buildLoadingScreen();
        }

        return Scaffold(
          body: AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return FadeTransition(
                opacity: _animation,
                child: SlideTransition(
                  position: Tween<Offset>(
                    begin: const Offset(0.1, 0),
                    end: Offset.zero,
                  ).animate(_animation),
                  child: _pages[_currentIndex],
                ),
              );
            },
          ),
          bottomNavigationBar: _buildBottomNavigationBar(),
        );
      },
    );
  }

  Widget _buildLoadingScreen() {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFFFF6B9D), Color(0xFF9B59B6)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                color: Colors.white,
                strokeWidth: 3,
              ),
              SizedBox(height: 24),
              Text(
                'Loading Friendy...',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBottomNavigationBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: _tabs.asMap().entries.map((entry) {
              final index = entry.key;
              final tab = entry.value;
              final isSelected = _currentIndex == index;

              return _buildTabItem(tab, isSelected, index);
            }).toList(),
          ),
        ),
      ),
    );
  }

  Widget _buildTabItem(MainAppTab tab, bool isSelected, int index) {
    return GestureDetector(
      onTap: () => _onTabTapped(index),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected
              ? tab.color.withValues(alpha: 0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 200),
              child: Icon(
                isSelected ? tab.activeIcon : tab.icon,
                key: ValueKey(isSelected),
                color: isSelected ? tab.color : Colors.grey[600],
                size: 24,
              ),
            ),
            const SizedBox(height: 4),
            AnimatedDefaultTextStyle(
              duration: const Duration(milliseconds: 200),
              style: GoogleFonts.poppins(
                color: isSelected ? tab.color : Colors.grey[600],
                fontSize: isSelected ? 12 : 11,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              ),
              child: Text(tab.label),
            ),
          ],
        ),
      ),
    );
  }
}

class MainAppTab {
  final IconData icon;
  final IconData activeIcon;
  final String label;
  final Color color;

  const MainAppTab({
    required this.icon,
    required this.activeIcon,
    required this.label,
    required this.color,
  });
}
